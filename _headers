# Cloudflare Pages Headers Configuration

/*
  # Security Headers
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=(), payment=(), usb=(), serial=(), bluetooth=(), magnetometer=(), gyroscope=(), accelerometer=()
  Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
  Cross-Origin-Embedder-Policy: require-corp
  Cross-Origin-Opener-Policy: same-origin
  Cross-Origin-Resource-Policy: same-origin

  # Content Security Policy (Enhanced)
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://forms.office.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https: blob:; font-src 'self' data:; connect-src 'self' https://forms.office.com; frame-src https://forms.office.com; object-src 'none'; base-uri 'self'; form-action 'self' https://forms.office.com; frame-ancestors 'none'; upgrade-insecure-requests;

# Static Assets Caching
/assets/*
  Cache-Control: public, max-age=31536000, immutable

# HTML Files
/*.html
  Cache-Control: public, max-age=3600, must-revalidate

# CSS Files
/*.css
  Cache-Control: public, max-age=31536000, immutable

# JavaScript Files
/*.js
  Cache-Control: public, max-age=31536000, immutable

# Images
/*.png
  Cache-Control: public, max-age=31536000, immutable
/*.jpg
  Cache-Control: public, max-age=31536000, immutable
/*.jpeg
  Cache-Control: public, max-age=31536000, immutable
/*.gif
  Cache-Control: public, max-age=31536000, immutable
/*.svg
  Cache-Control: public, max-age=31536000, immutable
/*.webp
  Cache-Control: public, max-age=31536000, immutable

# Manifest and Service Worker
/manifest.webmanifest
  Cache-Control: public, max-age=86400
/sw.js
  Cache-Control: public, max-age=0, must-revalidate

# Sitemap and Robots
/sitemap.xml
  Cache-Control: public, max-age=86400
/robots.txt
  Cache-Control: public, max-age=86400
