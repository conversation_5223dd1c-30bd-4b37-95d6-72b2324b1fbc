{"env": {"browser": true, "es2022": true, "node": true}, "extends": ["eslint:recommended", "prettier"], "plugins": ["html"], "parserOptions": {"ecmaVersion": 2022, "sourceType": "module"}, "rules": {"no-console": "warn", "no-debugger": "error", "no-unused-vars": "warn", "prefer-const": "error", "no-var": "error", "eqeqeq": "error", "curly": "error", "no-eval": "error", "no-implied-eval": "error", "no-new-func": "error", "no-script-url": "error"}, "ignorePatterns": ["dist/", "node_modules/", "*.min.js"]}