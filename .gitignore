# Node modules (if using any build tools locally)
node_modules/

# macOS system files
.DS_Store

# Thumbnails and system-generated files (Windows)
Thumbs.db
ehthumbs.db
Desktop.ini

# Log files
*.log

# Compiled output (if you ever add build steps)
dist/
build/

# Backup files
*.bak
*.tmp
*.swp
*.swo
*.swn

# Local environment configs (like VS Code settings)
.vscode/
.idea/

# OS or editor-specific swap/lock files
*.lock
*.pid

# Environment variable files (if you ever add them)
.env
.env.local
.env.*.local

# Ignore zip archives created during export or sharing
*.zip

# Modern build tools and caches
.cache/
.parcel-cache/
.eslintcache
.stylelintcache
.output/

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Cloudflare
.wrangler/

# Coverage reports
coverage/
*.lcov
