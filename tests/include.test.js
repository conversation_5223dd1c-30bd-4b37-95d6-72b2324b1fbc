/**
 * Tests for include.js functionality
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';

describe('ComponentLoader', () => {
  let mockFetch;

  beforeEach(() => {
    mockFetch = vi.fn();
    global.fetch = mockFetch;
  });

  it('should cache loaded content', async () => {
    const mockHTML = '<div>Test Content</div>';
    mockFetch.mockResolvedValueOnce({
      ok: true,
      text: () => Promise.resolve(mockHTML)
    });

    // Simulate ComponentLoader behavior
    const cache = new Map();
    const url = '/test.html';
    
    // First call
    const content1 = mockHTML;
    cache.set(url, content1);
    
    // Second call should use cache
    const content2 = cache.get(url);
    
    expect(content1).toBe(content2);
    expect(content1).toBe(mockHTML);
  });

  it('should handle fetch errors gracefully', async () => {
    mockFetch.mockRejectedValueOnce(new Error('Network error'));

    try {
      // Simulate error handling
      await fetch('/nonexistent.html');
    } catch (error) {
      expect(error.message).toBe('Network error');
    }
  });

  it('should update current year', () => {
    document.body.innerHTML = `
      <span id="year">2020</span>
      <span class="current-year">2021</span>
    `;

    const currentYear = new Date().getFullYear();
    const yearSpans = document.querySelectorAll('#year, .current-year');
    
    yearSpans.forEach(span => {
      span.textContent = currentYear;
    });

    expect(document.getElementById('year').textContent).toBe(currentYear.toString());
    expect(document.querySelector('.current-year').textContent).toBe(currentYear.toString());
  });

  it('should create and insert elements correctly', () => {
    const content = '<h1>Test Header</h1>';
    const parent = document.body;
    
    const element = document.createElement('header');
    element.innerHTML = content;
    parent.appendChild(element);

    const insertedElement = document.querySelector('header');
    expect(insertedElement).toBeTruthy();
    expect(insertedElement.innerHTML).toBe(content);
  });

  it('should handle Promise.allSettled results', async () => {
    const results = [
      { status: 'fulfilled', value: '<header>Header</header>' },
      { status: 'rejected', reason: new Error('Footer failed') }
    ];

    // Test fulfilled result
    expect(results[0].status).toBe('fulfilled');
    expect(results[0].value).toBe('<header>Header</header>');

    // Test rejected result
    expect(results[1].status).toBe('rejected');
    expect(results[1].reason).toBeInstanceOf(Error);
  });
});
