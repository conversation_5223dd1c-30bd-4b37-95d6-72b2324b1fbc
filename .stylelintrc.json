{"extends": ["stylelint-config-standard", "stylelint-prettier/recommended"], "rules": {"color-hex-case": "lower", "color-hex-length": "short", "declaration-block-trailing-semicolon": "always", "indentation": 2, "max-empty-lines": 2, "rule-empty-line-before": ["always", {"except": ["first-nested"], "ignore": ["after-comment"]}], "selector-pseudo-element-colon-notation": "double", "string-quotes": "single", "no-duplicate-selectors": true, "color-named": "never", "declaration-block-no-redundant-longhand-properties": true, "shorthand-property-no-redundant-values": true}, "ignoreFiles": ["dist/**/*.css", "node_modules/**/*.css"]}