{"name": "nexus-integrations-website", "version": "1.0.0", "description": "Modern website for Nexus Integrations LLC - Home & Small Business Tech Solutions", "main": "index.html", "scripts": {"dev": "vite", "build": "vite build && npm run generate-headers", "preview": "vite preview", "pages:build": "npm run build", "pages:dev": "npm run dev", "generate-headers": "node scripts/generate-headers.js", "optimize": "node scripts/optimize-performance.js", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "lint:css": "stylelint \"**/*.css\"", "lint:css:fix": "stylelint \"**/*.css\" --fix", "format": "prettier --write .", "format:check": "prettier --check .", "test": "vitest", "test:ui": "vitest --ui", "clean": "rm -rf dist", "serve": "serve dist"}, "keywords": ["business-website", "tech-solutions", "ai-integration", "maryland", "veteran-owned"], "author": "Nexus Integrations LLC", "license": "MIT", "devDependencies": {"vite": "^5.0.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-html": "^7.1.0", "prettier": "^3.2.0", "stylelint": "^16.2.0", "stylelint-config-standard": "^36.0.0", "stylelint-prettier": "^5.0.0", "vitest": "^1.2.0", "@vitest/ui": "^1.2.0", "jsdom": "^24.0.0", "@vitest/coverage-v8": "^1.2.0", "serve": "^14.2.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "cssnano": "^6.0.0", "husky": "^9.0.0", "lint-staged": "^15.2.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{css,scss}": ["stylelint --fix", "prettier --write"], "*.{html,json,md}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run test"}}}