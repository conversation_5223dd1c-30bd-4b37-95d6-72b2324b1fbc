#!/usr/bin/env node

import { copyFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';

const distDir = 'dist';
const sourceHeaders = '_headers';
const sourceRedirects = '_redirects';

// Ensure dist directory exists
if (!existsSync(distDir)) {
  mkdirSync(distDir, { recursive: true });
}

// Copy headers file to dist
try {
  copyFileSync(sourceHeaders, join(distDir, '_headers'));
  console.log('✅ Copied _headers to dist/');
} catch (error) {
  console.error('❌ Failed to copy _headers:', error.message);
}

// Copy redirects file to dist
try {
  copyFileSync(sourceRedirects, join(distDir, '_redirects'));
  console.log('✅ Copied _redirects to dist/');
} catch (error) {
  console.error('❌ Failed to copy _redirects:', error.message);
}

console.log('🚀 Cloudflare Pages configuration files ready!');
