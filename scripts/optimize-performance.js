#!/usr/bin/env node

/**
 * Performance optimization script for Nexus Integrations website
 * Handles critical CSS extraction, image optimization, and other performance tasks
 */

import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

/**
 * Extract critical CSS for above-the-fold content
 */
function extractCriticalCSS() {
  console.log('🎨 Extracting critical CSS...');
  
  const cssPath = join(projectRoot, 'assets/css/style.css');
  const fullCSS = readFileSync(cssPath, 'utf8');
  
  // Define critical CSS selectors (above-the-fold content)
  const criticalSelectors = [
    ':root',
    '*',
    '*::before',
    '*::after',
    'html',
    'body',
    'header',
    'nav',
    '.container',
    '.logo',
    'h1',
    '.tagline',
    'nav ul',
    'nav ul li',
    'nav ul li a',
    '.sr-only',
    '.skip-link',
    '@media (prefers-color-scheme: dark)',
    '@media (prefers-reduced-motion: reduce)'
  ];
  
  // Extract critical CSS rules
  let criticalCSS = '';
  const cssRules = fullCSS.split('}');
  
  cssRules.forEach(rule => {
    const trimmedRule = rule.trim();
    if (!trimmedRule) return;
    
    // Check if rule contains any critical selectors
    const isCritical = criticalSelectors.some(selector => 
      trimmedRule.includes(selector) || 
      trimmedRule.startsWith(selector) ||
      trimmedRule.includes(`${selector} `) ||
      trimmedRule.includes(`${selector}{`)
    );
    
    if (isCritical) {
      criticalCSS += trimmedRule + '}\n';
    }
  });
  
  // Ensure dist directory exists
  const distDir = join(projectRoot, 'dist');
  if (!existsSync(distDir)) {
    mkdirSync(distDir, { recursive: true });
  }
  
  // Write critical CSS
  const criticalCSSPath = join(distDir, 'critical.css');
  writeFileSync(criticalCSSPath, criticalCSS);
  
  console.log(`✅ Critical CSS extracted to ${criticalCSSPath}`);
  return criticalCSS;
}

/**
 * Generate optimized HTML with inlined critical CSS
 */
function optimizeHTML() {
  console.log('📄 Optimizing HTML files...');
  
  const htmlFiles = [
    'index.html',
    'about.html',
    'services.html',
    'contact.html',
    'recovery-connect.html',
    'offline.html'
  ];
  
  const criticalCSS = extractCriticalCSS();
  
  htmlFiles.forEach(filename => {
    const htmlPath = join(projectRoot, filename);
    if (!existsSync(htmlPath)) return;
    
    let html = readFileSync(htmlPath, 'utf8');
    
    // Inline critical CSS
    const criticalCSSTag = `<style>${criticalCSS}</style>`;
    html = html.replace('</head>', `  ${criticalCSSTag}\n</head>`);
    
    // Make main CSS non-blocking
    html = html.replace(
      '<link rel="stylesheet" href="/assets/css/style.css" />',
      '<link rel="preload" href="/assets/css/style.css" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">\n  <noscript><link rel="stylesheet" href="/assets/css/style.css"></noscript>'
    );
    
    // Add resource hints
    const resourceHints = `
  <!-- Resource Hints -->
  <link rel="dns-prefetch" href="//forms.office.com">
  <link rel="preload" href="/assets/images/nexus-logo.png" as="image">
  <link rel="preload" href="/assets/js/main.js" as="script">`;
    
    html = html.replace('  <!-- Preconnect to external domains -->', resourceHints);
    
    // Write optimized HTML to dist
    const distDir = join(projectRoot, 'dist');
    if (!existsSync(distDir)) {
      mkdirSync(distDir, { recursive: true });
    }
    
    const distPath = join(distDir, filename);
    writeFileSync(distPath, html);
    
    console.log(`✅ Optimized ${filename}`);
  });
}

/**
 * Generate performance budget report
 */
function generatePerformanceBudget() {
  console.log('📊 Generating performance budget...');
  
  const budget = {
    "budget": [
      {
        "resourceSizes": [
          {
            "resourceType": "script",
            "budget": 150
          },
          {
            "resourceType": "total",
            "budget": 500
          },
          {
            "resourceType": "stylesheet",
            "budget": 50
          },
          {
            "resourceType": "image",
            "budget": 200
          }
        ],
        "resourceCounts": [
          {
            "resourceType": "script",
            "budget": 5
          },
          {
            "resourceType": "stylesheet",
            "budget": 2
          },
          {
            "resourceType": "third-party",
            "budget": 3
          }
        ]
      }
    ]
  };
  
  const budgetPath = join(projectRoot, 'performance-budget.json');
  writeFileSync(budgetPath, JSON.stringify(budget, null, 2));
  
  console.log(`✅ Performance budget created at ${budgetPath}`);
}

/**
 * Main optimization function
 */
function optimize() {
  console.log('🚀 Starting performance optimizations...\n');
  
  try {
    extractCriticalCSS();
    optimizeHTML();
    generatePerformanceBudget();
    
    console.log('\n✨ Performance optimizations completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Test the optimized files in the dist/ directory');
    console.log('2. Run lighthouse audits to verify improvements');
    console.log('3. Deploy the optimized version to Cloudflare Pages');
    
  } catch (error) {
    console.error('❌ Optimization failed:', error);
    process.exit(1);
  }
}

// Run optimization if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  optimize();
}

export { extractCriticalCSS, optimizeHTML, generatePerformanceBudget };
