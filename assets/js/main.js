/**
 * Main application JavaScript
 * Modern ES6+ implementation with performance optimizations
 */

class NexusApp {
  constructor() {
    this.isInitialized = false;
    this.observers = new Map();
    this.init();
  }

  /**
   * Initialize the application
   */
  init() {
    if (this.isInitialized) return;

    this.setupIntersectionObserver();
    this.setupSmoothScrolling();
    this.setupFormEnhancements();
    this.setupAccessibilityFeatures();
    this.setupPerformanceOptimizations();

    this.isInitialized = true;
    console.log('Nexus Integrations app initialized');
  }

  /**
   * Set up intersection observer for animations and lazy loading
   */
  setupIntersectionObserver() {
    if (!('IntersectionObserver' in window)) return;

    const observerOptions = {
      threshold: 0.1,
      rootMargin: '50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
          observer.unobserve(entry.target);
        }
      });
    }, observerOptions);

    // Observe sections for animation
    const sections = document.querySelectorAll('main section');
    sections.forEach(section => {
      section.classList.add('animate-ready');
      observer.observe(section);
    });

    this.observers.set('intersection', observer);
  }

  /**
   * Enhanced smooth scrolling for navigation links
   */
  setupSmoothScrolling() {
    document.addEventListener('click', (e) => {
      const link = e.target.closest('a[href^="#"]');
      if (!link) return;

      e.preventDefault();
      const targetId = link.getAttribute('href').slice(1);
      const targetElement = document.getElementById(targetId);

      if (targetElement) {
        const headerHeight = document.querySelector('header')?.offsetHeight || 0;
        const targetPosition = targetElement.offsetTop - headerHeight - 20;

        window.scrollTo({
          top: targetPosition,
          behavior: 'smooth'
        });

        // Update focus for accessibility
        targetElement.focus({ preventScroll: true });
      }
    });
  }

  /**
   * Enhance forms with better UX
   */
  setupFormEnhancements() {
    const forms = document.querySelectorAll('form');

    forms.forEach(form => {
      // Add loading states for form submissions
      form.addEventListener('submit', (e) => {
        const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
        if (submitButton) {
          submitButton.disabled = true;
          submitButton.textContent = 'Sending...';

          // Re-enable after 5 seconds as fallback
          setTimeout(() => {
            submitButton.disabled = false;
            submitButton.textContent = 'Send Message';
          }, 5000);
        }
      });

      // Enhanced input validation feedback
      const inputs = form.querySelectorAll('input, textarea');
      inputs.forEach(input => {
        input.addEventListener('blur', () => {
          this.validateInput(input);
        });
      });
    });
  }

  /**
   * Validate individual input and provide feedback
   * @param {HTMLInputElement} input - The input element to validate
   */
  validateInput(input) {
    const isValid = input.checkValidity();
    const feedback = input.parentNode.querySelector('.validation-feedback');

    if (feedback) {
      feedback.textContent = isValid ? '' : input.validationMessage;
      feedback.setAttribute('aria-live', 'polite');
    }

    input.setAttribute('aria-invalid', !isValid);
  }

  /**
   * Setup accessibility enhancements
   */
  setupAccessibilityFeatures() {
    // Skip to main content link
    this.createSkipLink();

    // Enhanced keyboard navigation
    this.setupKeyboardNavigation();

    // Focus management
    this.setupFocusManagement();
  }

  /**
   * Create skip to main content link for screen readers
   */
  createSkipLink() {
    const skipLink = document.createElement('a');
    skipLink.href = '#main';
    skipLink.textContent = 'Skip to main content';
    skipLink.className = 'skip-link sr-only';
    skipLink.addEventListener('focus', () => {
      skipLink.classList.remove('sr-only');
    });
    skipLink.addEventListener('blur', () => {
      skipLink.classList.add('sr-only');
    });

    document.body.insertBefore(skipLink, document.body.firstChild);
  }

  /**
   * Enhanced keyboard navigation
   */
  setupKeyboardNavigation() {
    document.addEventListener('keydown', (e) => {
      // Escape key to close modals or return focus
      if (e.key === 'Escape') {
        const activeModal = document.querySelector('.modal.active');
        if (activeModal) {
          this.closeModal(activeModal);
        }
      }
    });
  }

  /**
   * Focus management for better accessibility
   */
  setupFocusManagement() {
    // Trap focus in modals
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Tab') {
        const modal = document.querySelector('.modal.active');
        if (modal) {
          this.trapFocus(e, modal);
        }
      }
    });
  }

  /**
   * Performance optimizations
   */
  setupPerformanceOptimizations() {
    // Preload critical resources
    this.preloadCriticalResources();

    // Lazy load images
    this.setupLazyLoading();

    // Debounced resize handler
    let resizeTimeout;
    window.addEventListener('resize', () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        this.handleResize();
      }, 250);
    });
  }

  /**
   * Preload critical resources for better performance
   */
  preloadCriticalResources() {
    const criticalImages = [
      '/assets/images/nexus-logo.png'
    ];

    criticalImages.forEach(src => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = src;
      document.head.appendChild(link);
    });
  }

  /**
   * Setup lazy loading for images
   */
  setupLazyLoading() {
    if (!('IntersectionObserver' in window)) return;

    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          if (img.dataset.src) {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
            imageObserver.unobserve(img);
          }
        }
      });
    });

    const lazyImages = document.querySelectorAll('img[data-src]');
    lazyImages.forEach(img => imageObserver.observe(img));
  }

  /**
   * Handle window resize events
   */
  handleResize() {
    // Update any size-dependent calculations
    console.log('Window resized, updating layout calculations');
  }

  /**
   * Cleanup method for when the app is destroyed
   */
  destroy() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
    this.isInitialized = false;
  }
}

// Initialize app when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  window.nexusApp = new NexusApp();
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  if (window.nexusApp) {
    window.nexusApp.destroy();
  }
});