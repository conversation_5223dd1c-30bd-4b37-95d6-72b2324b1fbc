/**
 * Modern include system with error handling and performance optimizations
 */

class ComponentLoader {
  constructor() {
    this.cache = new Map();
    this.loadingPromises = new Map();
  }

  /**
   * Load and cache HTML content
   * @param {string} url - The URL to fetch
   * @returns {Promise<string>} The HTML content
   */
  async loadContent(url) {
    // Return cached content if available
    if (this.cache.has(url)) {
      return this.cache.get(url);
    }

    // Return existing promise if already loading
    if (this.loadingPromises.has(url)) {
      return this.loadingPromises.get(url);
    }

    // Create new loading promise
    const loadingPromise = this.fetchContent(url);
    this.loadingPromises.set(url, loadingPromise);

    try {
      const content = await loadingPromise;
      this.cache.set(url, content);
      return content;
    } catch (error) {
      console.error(`Failed to load content from ${url}:`, error);
      throw error;
    } finally {
      this.loadingPromises.delete(url);
    }
  }

  /**
   * Fetch content with timeout and error handling
   * @param {string} url - The URL to fetch
   * @returns {Promise<string>} The HTML content
   */
  async fetchContent(url) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

    try {
      const response = await fetch(url, {
        signal: controller.signal,
        headers: {
          'Accept': 'text/html',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.text();
    } finally {
      clearTimeout(timeoutId);
    }
  }

  /**
   * Create and insert element with content
   * @param {string} tagName - The tag name for the element
   * @param {string} content - The HTML content
   * @param {Element} parent - The parent element
   * @param {Element|null} before - Element to insert before (null for append)
   */
  insertElement(tagName, content, parent, before = null) {
    const element = document.createElement(tagName);
    element.innerHTML = content;

    if (before) {
      parent.insertBefore(element, before);
    } else {
      parent.appendChild(element);
    }

    return element;
  }

  /**
   * Update year spans with current year
   */
  updateCurrentYear() {
    const yearSpans = document.querySelectorAll('#year, .current-year');
    const currentYear = new Date().getFullYear();

    yearSpans.forEach(span => {
      span.textContent = currentYear;
    });
  }
}

// Initialize component loader
const componentLoader = new ComponentLoader();

// Load components when DOM is ready
document.addEventListener('DOMContentLoaded', async () => {
  try {
    // Load header and footer in parallel for better performance
    const [headerContent, footerContent] = await Promise.allSettled([
      componentLoader.loadContent('/includes/header.html'),
      componentLoader.loadContent('/includes/footer.html')
    ]);

    // Insert header if loaded successfully
    if (headerContent.status === 'fulfilled') {
      componentLoader.insertElement(
        'header',
        headerContent.value,
        document.body,
        document.body.firstChild
      );
    } else {
      console.warn('Header failed to load:', headerContent.reason);
    }

    // Insert footer if loaded successfully
    if (footerContent.status === 'fulfilled') {
      componentLoader.insertElement(
        'footer',
        footerContent.value,
        document.body
      );
    } else {
      console.warn('Footer failed to load:', footerContent.reason);
    }

    // Update current year in any loaded content
    componentLoader.updateCurrentYear();

  } catch (error) {
    console.error('Failed to load page components:', error);
  }
});
