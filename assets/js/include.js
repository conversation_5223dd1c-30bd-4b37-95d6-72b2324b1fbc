document.addEventListener("DOMContentLoaded", () => {
  // Load header
  fetch("/includes/header.html")
    .then(res => res.text())
    .then(data => {
      const header = document.createElement('header');
      header.innerHTML = data;
      document.body.insertBefore(header, document.body.firstChild);

      // Set current year in footer if it's also part of header or footer
      const yearSpan = document.getElementById('year');
      if (yearSpan) yearSpan.textContent = new Date().getFullYear();
    });

  // Load footer
  fetch("/includes/footer.html")
    .then(res => res.text())
    .then(data => {
      const footer = document.createElement('footer');
      footer.innerHTML = data;
      document.body.appendChild(footer);

      // Set current year if header doesn't have it
      const yearSpan = document.getElementById('year');
      if (yearSpan) yearSpan.textContent = new Date().getFullYear();
    });
});
