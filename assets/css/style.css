/* === Reset & Base === */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  background-color: #ffffff; /* Light mode default */
  color: #0B0E11;
}

/* === Container === */
.container {
  width: 90%;
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem 0;
}

/* === Header === */
header {
  background: #0B0E11; /* Neutral 90 */
  color: #E4E9ED;      /* On Dark Text */
  text-align: center;
  border-bottom: 4px solid #00FF94; /* Accent 500 */
}

header .logo {
  max-width: 100px;
  margin-bottom: 1rem;
}

header h1 {
  font-size: 2rem;
  color: #1D9BF0; /* Primary 600 */
}

header .tagline {
  color: #56B4F8; /* Primary 300 */
  margin-bottom: 1rem;
}

/* === Navigation === */
nav {
  background: #142d4c; /* Slightly muted for contrast */
}

nav ul {
  display: flex;
  justify-content: center;
  list-style: none;
  padding: 0.5rem 0;
}

nav ul li {
  margin: 0 1rem;
}

nav ul li a {
  color: #E4E9ED; /* On Dark Text */
  text-decoration: none;
  font-weight: bold;
  transition: color 0.3s;
}

nav ul li a:hover,
nav ul li a:focus {
  color: #00FF94; /* Accent 500 */
}

nav ul li a[aria-current="page"] {
  border-bottom: 2px solid #00FF94;
}

/* === Line spacing and paragraph styling === */
main p {
  margin-bottom: 1rem;
}


/* === Main === */
main section {
  padding: 2rem 0;
  border-bottom: 1px solid #ddd;
}

main h2 {
  color: #1D9BF0; /* Primary 600 */
  margin-bottom: 1rem;
}

ul li {
  margin-bottom: 0.5rem;
}

/* === Links === */
a {
  color: #1D9BF0; /* Primary 600 */
  text-decoration: none;
}

a:hover,
a:focus {
  color: #00FF94; /* Accent 500 */
  text-decoration: underline;
}

/* === Footer === */
footer {
  background: #0B0E11; /* Neutral 90 */
  color: #E4E9ED;      /* On Dark Text */
  text-align: center;
  padding: 1rem 0;
  font-size: 0.9rem;
}

footer a {
  color: #56B4F8; /* Primary 300 */
}

footer a:hover,
footer a:focus {
  color: #00FF94; /* Accent 500 */
}

/* === Dark Mode === */
@media (prefers-color-scheme: dark) {
  body {
    background-color: #0B0E11;
    color: #E4E9ED;
  }

  main h2 {
    color: #56B4F8; /* Primary 300 on dark */
  }

  nav {
    background: #0B0E11;
  }
}

/* === CTA Button Styling === */
.cta-button {
  display: inline-block;
  background-color: #00FF94; /* Accent 500 neon green */
  color: #0B0E11; /* On Light Text */
  font-weight: bold;
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  margin-top: 1rem;
  transition: background-color 0.3s, color 0.3s;
}

.cta-button:hover,
.cta-button:focus {
  background-color: #00CC77; /* Accent 700 on hover */
  color: #0B0E11; /* On Light Text (stays consistent) */
}

/* Form container styling */
.form-container {
  margin: 1.5rem 0;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f9f9f9;
}

@media (prefers-color-scheme: dark) {
  .form-container {
    background-color: #142d4c;
    border-color: #1D9BF0;
  }
}
