/* === CSS Custom Properties (Variables) === */
:root {
  /* Colors */
  --color-primary-300: #56B4F8;
  --color-primary-600: #1D9BF0;
  --color-accent-500: #00FF94;
  --color-accent-700: #00CC77;
  --color-neutral-90: #0B0E11;
  --color-on-dark: #E4E9ED;
  --color-on-light: #0B0E11;
  --color-background-light: #ffffff;
  --color-background-dark: #0B0E11;
  --color-surface-dark: #142d4c;

  /* Typography */
  --font-family-primary: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 2rem;
  --line-height-base: 1.6;
  --line-height-tight: 1.4;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* Layout */
  --container-max-width: 1200px;
  --container-padding: var(--spacing-xl);
  --border-radius: 0.5rem;
  --border-radius-sm: 0.25rem;

  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-base: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-base: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* === Reset & Base === */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  background-color: var(--color-background-light);
  color: var(--color-on-light);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* === Container === */
.container {
  width: min(90%, var(--container-max-width));
  margin-inline: auto;
  padding-block: var(--container-padding);
  padding-inline: var(--spacing-md);
}

@media (min-width: 768px) {
  .container {
    padding-inline: var(--spacing-xl);
  }
}

/* === Header === */
header {
  background: var(--color-neutral-90);
  color: var(--color-on-dark);
  text-align: center;
  border-bottom: 4px solid var(--color-accent-500);
  position: relative;
}

header .container {
  display: grid;
  gap: var(--spacing-md);
  padding-block: var(--spacing-xl);
}

header .logo {
  max-width: 100px;
  height: auto;
  margin-inline: auto;
  transition: transform var(--transition-base);
}

header .logo:hover {
  transform: scale(1.05);
}

header h1 {
  font-size: clamp(var(--font-size-2xl), 4vw, var(--font-size-3xl));
  color: var(--color-primary-600);
  font-weight: 700;
  line-height: var(--line-height-tight);
}

header .tagline {
  color: var(--color-primary-300);
  font-size: var(--font-size-lg);
  font-weight: 500;
}

/* === Navigation === */
nav {
  background: var(--color-surface-dark);
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: var(--shadow-base);
}

nav ul {
  display: flex;
  justify-content: center;
  align-items: center;
  list-style: none;
  padding-block: var(--spacing-md);
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

@media (min-width: 768px) {
  nav ul {
    gap: var(--spacing-xl);
  }
}

nav ul li a {
  color: var(--color-on-dark);
  text-decoration: none;
  font-weight: 600;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-base);
  position: relative;
  display: block;
}

nav ul li a:hover,
nav ul li a:focus {
  color: var(--color-accent-500);
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

nav ul li a[aria-current="page"] {
  color: var(--color-accent-500);
  background-color: rgba(0, 255, 148, 0.1);
}

nav ul li a[aria-current="page"]::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 2px;
  background: var(--color-accent-500);
  border-radius: 1px;
}

/* === Main Content === */
main {
  min-height: 70vh;
}

main section {
  padding-block: var(--spacing-2xl);
  border-bottom: 1px solid hsl(0 0% 0% / 0.1);
  position: relative;
}

main section:last-child {
  border-bottom: none;
}

main h2 {
  color: var(--color-primary-600);
  font-size: clamp(var(--font-size-xl), 3vw, var(--font-size-2xl));
  font-weight: 700;
  margin-bottom: var(--spacing-lg);
  line-height: var(--line-height-tight);
}

main p {
  margin-bottom: var(--spacing-md);
  max-width: 65ch;
  line-height: var(--line-height-base);
}

main ul {
  display: grid;
  gap: var(--spacing-sm);
  margin-block: var(--spacing-md);
}

main ul li {
  position: relative;
  padding-left: var(--spacing-lg);
  line-height: var(--line-height-base);
}

main ul li::before {
  content: '→';
  position: absolute;
  left: 0;
  color: var(--color-accent-500);
  font-weight: bold;
}

/* === Links === */
a {
  color: var(--color-primary-600);
  text-decoration: none;
  transition: all var(--transition-base);
  position: relative;
}

a:hover,
a:focus {
  color: var(--color-accent-500);
  text-decoration: underline;
  text-decoration-thickness: 2px;
  text-underline-offset: 2px;
}

a:focus-visible {
  outline: 2px solid var(--color-accent-500);
  outline-offset: 2px;
  border-radius: var(--border-radius-sm);
}

/* === Footer === */
footer {
  background: var(--color-neutral-90);
  color: var(--color-on-dark);
  text-align: center;
  padding-block: var(--spacing-xl);
  margin-top: auto;
}

footer .container {
  display: grid;
  gap: var(--spacing-sm);
}

footer p {
  font-size: 0.9rem;
  line-height: var(--line-height-base);
  margin: 0;
}

footer a {
  color: var(--color-primary-300);
  font-weight: 500;
}

footer a:hover,
footer a:focus {
  color: var(--color-accent-500);
}

/* === Dark Mode === */
@media (prefers-color-scheme: dark) {
  :root {
    --color-background-light: var(--color-background-dark);
    --color-on-light: var(--color-on-dark);
  }

  body {
    background-color: var(--color-background-dark);
    color: var(--color-on-dark);
  }

  main h2 {
    color: var(--color-primary-300);
  }

  main section {
    border-bottom-color: hsl(0 0% 100% / 0.1);
  }

  nav {
    background: var(--color-neutral-90);
  }

  .form-container {
    background-color: var(--color-surface-dark);
    border-color: var(--color-primary-600);
  }
}

/* === CTA Button Styling === */
.cta-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  background-color: var(--color-accent-500);
  color: var(--color-on-light);
  font-weight: 700;
  font-size: var(--font-size-base);
  text-decoration: none;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--border-radius);
  margin-top: var(--spacing-lg);
  transition: all var(--transition-base);
  box-shadow: var(--shadow-base);
  border: none;
  cursor: pointer;
  min-height: 44px; /* Accessibility: minimum touch target */
}

.cta-button:hover,
.cta-button:focus {
  background-color: var(--color-accent-700);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.cta-button:focus-visible {
  outline: 2px solid var(--color-accent-500);
  outline-offset: 2px;
}

.cta-button:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* Form container styling */
.form-container {
  margin-block: var(--spacing-lg);
  padding: var(--spacing-lg);
  border: 1px solid hsl(0 0% 0% / 0.1);
  border-radius: var(--border-radius);
  background-color: hsl(0 0% 98%);
  box-shadow: var(--shadow-sm);
}

/* === Utility Classes === */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-accent-500);
  color: var(--color-on-light);
  padding: var(--spacing-sm) var(--spacing-md);
  text-decoration: none;
  border-radius: var(--border-radius-sm);
  z-index: 1000;
  font-weight: 600;
  transition: top var(--transition-base);
}

.skip-link:focus {
  top: 6px;
}

.text-center {
  text-align: center;
}

.text-balance {
  text-wrap: balance;
}

/* === Animation Classes === */
.animate-ready {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* === Focus Management === */
*:focus-visible {
  outline: 2px solid var(--color-accent-500);
  outline-offset: 2px;
  border-radius: var(--border-radius-sm);
}

/* Remove default focus styles for mouse users */
*:focus:not(:focus-visible) {
  outline: none;
}

/* === Reduced Motion Support === */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .animate-ready {
    opacity: 1;
    transform: none;
  }
}

/* === Responsive Design === */
@media (max-width: 767px) {
  nav ul {
    padding-inline: var(--spacing-md);
  }

  nav ul li a {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.9rem;
  }

  header h1 {
    font-size: var(--font-size-2xl);
  }

  main section {
    padding-block: var(--spacing-xl);
  }
}
