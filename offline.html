<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Offline - Nexus Integrations LLC</title>
  <meta name="description" content="You're currently offline. Please check your internet connection.">
  <meta name="robots" content="noindex, nofollow">
  
  <link rel="stylesheet" href="/assets/css/style.css" />
  <link rel="icon" href="/assets/images/nexus-temp-favicon.png" type="image/png">
  <meta name="theme-color" content="#1D9BF0">
</head>
<body>
  <header role="banner">
    <div class="container">
      <img src="/assets/images/nexus-logo.png" alt="Nexus Integrations LLC Logo" class="logo" width="100" height="100" />
      <h1 class="text-balance">Nexus Integrations LLC</h1>
      <p class="tagline text-balance">Your Partner in Tech Solutions & AI Integration</p>
    </div>
  </header>

  <main id="main" role="main" class="container">
    <section id="offline" aria-labelledby="offline-heading" class="text-center">
      <h2 id="offline-heading">You're Currently Offline</h2>
      <p>It looks like you've lost your internet connection. Don't worry, you can still browse the pages you've already visited.</p>
      
      <div style="margin: 2rem 0;">
        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="color: var(--color-primary-600);">
          <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
          <circle cx="12" cy="12" r="3"></circle>
          <path d="M12 1v6m0 6v6"></path>
        </svg>
      </div>
      
      <p>
        <button onclick="window.location.reload()" class="cta-button">
          Try Again
        </button>
      </p>
      
      <p style="margin-top: 2rem;">
        <a href="/">Return to Homepage</a>
      </p>
    </section>
  </main>

  <footer role="contentinfo">
    <div class="container">
      <p>&copy; <span class="current-year">2024</span> Nexus Integrations LLC. All rights reserved.</p>
    </div>
  </footer>

  <script>
    // Update current year
    document.querySelector('.current-year').textContent = new Date().getFullYear();
    
    // Check if back online
    window.addEventListener('online', () => {
      window.location.reload();
    });
  </script>
</body>
</html>
