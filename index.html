<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Nexus Integrations LLC - Home & Small Business Tech Solutions</title>
  <meta name="description" content="Home & Small Business Tech and AI Integrations in Anne Arundel County, Maryland. On-site & remote support and consulting services.">
  <meta name="keywords" content="tech support, AI integration, small business, Maryland, veteran-owned, network setup, automation">
  <meta name="author" content="Nexus Integrations LLC">
  <meta name="robots" content="index, follow">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://nexus-integrations.net/">
  <meta property="og:title" content="Nexus Integrations LLC - Home & Small Business Tech Solutions">
  <meta property="og:description" content="Home & Small Business Tech and AI Integrations in Maryland. On-site & remote support services.">
  <meta property="og:image" content="https://nexus-integrations.net/assets/images/nexus-logo.png">
  <meta property="og:site_name" content="Nexus Integrations LLC">
  <meta property="og:locale" content="en_US">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="https://nexus-integrations.net/">
  <meta property="twitter:title" content="Nexus Integrations LLC - Home & Small Business Tech Solutions">
  <meta property="twitter:description" content="Home & Small Business Tech and AI Integrations in Maryland. On-site & remote support services.">
  <meta property="twitter:image" content="https://nexus-integrations.net/assets/images/nexus-logo.png">

  <!-- Canonical URL -->
  <link rel="canonical" href="https://nexus-integrations.net/">

  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://forms.office.com">

  <!-- Stylesheets -->
  <link rel="stylesheet" href="/assets/css/style.css" />

  <!-- Icons and Manifest -->
  <link rel="icon" href="/assets/images/nexus-temp-favicon.png" type="image/png">
  <link rel="apple-touch-icon" href="/assets/images/nexus-temp-favicon.png">
  <link rel="shortcut icon" href="/assets/images/nexus-temp-favicon.png" type="image/png">
  <link rel="manifest" href="/manifest.webmanifest">

  <!-- Theme color for mobile browsers -->
  <meta name="theme-color" content="#1D9BF0">
  <meta name="msapplication-TileColor" content="#1D9BF0">

  <!-- Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": "Nexus Integrations LLC",
    "description": "Home & Small Business Tech and AI Integrations",
    "url": "https://nexus-integrations.net",
    "telephone": "",
    "email": "<EMAIL>",
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "Hanover",
      "addressRegion": "MD",
      "addressCountry": "US"
    },
    "serviceArea": {
      "@type": "State",
      "name": "Maryland"
    },
    "priceRange": "$$",
    "openingHours": "Mo-Fr 09:00-17:00"
  }
  </script>
</head>
<body>
  <header role="banner">
    <div class="container">
      <img src="/assets/images/nexus-logo.png" alt="Nexus Integrations LLC Logo" class="logo" width="100" height="100" />
      <h1 class="text-balance">Nexus Integrations LLC</h1>
      <p class="tagline text-balance">Your Partner in Tech Solutions & AI Integration</p>
    </div>
    <nav role="navigation" aria-label="Main navigation">
      <ul>
        <li><a href="/" aria-current="page">Home</a></li>
        <li><a href="/about">About</a></li>
        <li><a href="/services">Services</a></li>
        <li><a href="/recovery-connect">Recovery Connect</a></li>
        <li><a href="/contact">Contact</a></li>
      </ul>
    </nav>
  </header>

  <main id="main" role="main" class="container">
    <section id="about" aria-labelledby="about-heading">
      <h2 id="about-heading" class="text-balance">About Us</h2>
      <p class="text-balance">
        Nexus Integrations LLC is a veteran-owned technology solutions company based in Maryland.
        We specialize in modernizing home and small business operations through tailored
        AI integration, secure networks, and reliable troubleshooting—onsite or remote.
      </p>
    </section>

    <section id="services" aria-labelledby="services-heading">
      <h2 id="services-heading">Our Services</h2>
      <ul role="list">
        <li>Home & Small Business Tech Integration</li>
        <li>AI Tools & Automation Setup</li>
        <li>Network Design, Troubleshooting & Support</li>
        <li>Custom Software & App Development</li>
      </ul>
    </section>

    <section id="contact" aria-labelledby="contact-heading">
      <h2 id="contact-heading">Contact Us</h2>
      <address>
        <p>Email: <a href="mailto:<EMAIL>" aria-label="Send email to Nexus Integrations"><EMAIL></a></p>
        <p>Location: Hanover, MD</p>
      </address>
      <p>
        <a href="https://forms.office.com/Pages/ResponsePage.aspx?id=vOzE9gBw1E-Up8eaf8YTP3RKOJpsTPBGoOkemq4J3DFUQkEwQlVYWk5VQ1ZDU1Y2WFM3T0tNOEpETC4u"
           class="cta-button"
           target="_blank"
           rel="noopener noreferrer"
           aria-label="Open contact form in new window">
          Get in Touch
        </a>
      </p>
    </section>
  </main>

  <footer role="contentinfo">
    <div class="container">
      <p>&copy; <span id="year" class="current-year">2024</span> Nexus Integrations LLC. All rights reserved.</p>
      <p>Registered in Maryland •
        <a href="/privacy">Privacy</a> •
        <a href="/terms">Terms</a> •
        <a href="/disclaimer">Disclaimer</a>
      </p>
    </div>
  </footer>

  <!-- Scripts -->
  <script src="/assets/js/include.js" defer></script>
  <script src="/assets/js/main.js" defer></script>

  <!-- Service Worker Registration -->
  <script>
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
          .then((registration) => {
            console.log('SW registered: ', registration);
          })
          .catch((registrationError) => {
            console.log('SW registration failed: ', registrationError);
          });
      });
    }
  </script>
</body>
</html>

