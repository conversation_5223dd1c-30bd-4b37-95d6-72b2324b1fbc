import { defineConfig } from 'vite';
import { resolve } from 'path';

export default defineConfig({
  // Base public path when served in development or production
  base: '/',

  // Build configuration optimized for Cloudflare Pages
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false, // Disable sourcemaps for production
    minify: 'terser',
    target: 'es2020', // Modern browsers for better performance
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html'),
        about: resolve(__dirname, 'about.html'),
        services: resolve(__dirname, 'services.html'),
        contact: resolve(__dirname, 'contact.html'),
        recovery: resolve(__dirname, 'recovery-connect.html'),
        privacy: resolve(__dirname, 'legal/privacy-policy.html'),
        terms: resolve(__dirname, 'legal/terms-of-service.html'),
        disclaimer: resolve(__dirname, 'legal/donation-disclaimer.html'),
        '404': resolve(__dirname, '404.html')
      },
      output: {
        // Optimize chunk splitting for Cloudflare's edge caching
        manualChunks: {
          vendor: ['vite']
        },
        assetFileNames: 'assets/[name]-[hash][extname]',
        chunkFileNames: 'assets/[name]-[hash].js',
        entryFileNames: 'assets/[name]-[hash].js'
      }
    },
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info']
      },
      mangle: {
        safari10: true
      }
    },
    // Optimize for Cloudflare Pages
    chunkSizeWarningLimit: 1000,
    reportCompressedSize: false
  },
  
  // Development server configuration
  server: {
    port: 3000,
    open: true,
    cors: true,
    host: true
  },
  
  // Preview server configuration
  preview: {
    port: 4173,
    open: true
  },
  
  // CSS configuration
  css: {
    postcss: './postcss.config.js',
    devSourcemap: true
  },
  
  // Asset handling
  assetsInclude: ['**/*.png', '**/*.jpg', '**/*.jpeg', '**/*.gif', '**/*.svg', '**/*.webp'],
  
  // Plugin configuration
  plugins: [],
  
  // Optimization
  optimizeDeps: {
    include: []
  }
});
